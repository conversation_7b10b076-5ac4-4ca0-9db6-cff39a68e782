import 'dart:io';
import 'package:newrelic_mobile/newrelic_mobile.dart';
import 'package:newrelic_mobile/config.dart';
import 'package:newrelic_mobile/network_failure.dart';
import 'package:gameflex_mobile/config/environment_config.dart';
import 'package:gameflex_mobile/utils/app_logger.dart';

/// Service for managing New Relic initialization and configuration
/// Handles environment-specific tokens and configuration settings
class NewRelicService {
  static final NewRelicService _instance = NewRelicService._internal();
  static NewRelicService get instance => _instance;

  NewRelicService._internal();

  bool _isInitialized = false;

  /// Check if New Relic has been initialized
  bool get isInitialized => _isInitialized;

  /// Initialize New Relic with environment-specific configuration
  Future<void> initialize() async {
    if (_isInitialized) {
      AppLogger.info('New Relic already initialized');
      return;
    }

    try {
      final appToken = _getAppToken();

      if (appToken.isEmpty) {
        AppLogger.warning(
          'New Relic token not found for current environment and platform',
        );
        return;
      }

      final config = _createConfig(appToken);

      AppLogger.info(
        'Initializing New Relic for ${EnvironmentConfig.currentEnvironment} environment',
      );
      AppLogger.info('Platform: ${Platform.isIOS ? 'iOS' : 'Android'}');

      await NewrelicMobile.instance.start(config, () {
        AppLogger.info('New Relic initialized successfully');
        _isInitialized = true;

        // Set device and network information for analytics
        setDeviceAndNetworkInfo();
      });
    } catch (e) {
      AppLogger.error('Failed to initialize New Relic: $e');
    }
  }

  /// Get the appropriate app token based on current environment and platform
  String _getAppToken() {
    final environment = EnvironmentConfig.currentEnvironment;
    final tokens = EnvironmentConfig.newRelicTokens[environment];

    if (tokens == null) {
      AppLogger.warning(
        'No New Relic tokens configured for environment: $environment',
      );
      return '';
    }

    if (Platform.isIOS) {
      return tokens['ios'] ?? '';
    } else if (Platform.isAndroid) {
      return tokens['android'] ?? '';
    }

    return '';
  }

  /// Create New Relic configuration based on environment
  Config _createConfig(String appToken) {
    final isDevelopment = EnvironmentConfig.isDevelopment;

    return Config(
      accessToken: appToken,

      // Analytics and Event Collection
      analyticsEventEnabled: true,
      printStatementAsEventsEnabled: isDevelopment, // Only in dev for debugging
      // Network Monitoring
      networkErrorRequestEnabled: true,
      networkRequestEnabled: true,
      httpInstrumentationEnabled: true, // Re-enabled for comprehensive tracking
      httpResponseBodyCaptureEnabled:
          isDevelopment, // Only in dev for debugging
      // Crash Reporting
      crashReportingEnabled: true,

      // Interaction Tracing
      interactionTracingEnabled: true,

      // iOS specific options
      webViewInstrumentation: Platform.isIOS,

      // Logging - more verbose in development
      loggingEnabled: isDevelopment,
    );
  }

  /// Set custom attributes for the current session
  void setCustomAttributes(Map<String, dynamic> attributes) {
    if (!_isInitialized) {
      AppLogger.warning(
        'New Relic not initialized, cannot set custom attributes',
      );
      return;
    }

    attributes.forEach((key, value) {
      NewrelicMobile.instance.setAttribute(key, value);
    });
  }

  /// Record a custom event
  void recordCustomEvent(String eventType, Map<String, dynamic> attributes) {
    if (!_isInitialized) {
      AppLogger.warning(
        'New Relic not initialized, cannot record custom event',
      );
      return;
    }

    NewrelicMobile.instance.recordCustomEvent(
      eventType,
      eventAttributes: attributes,
    );
  }

  /// Record a breadcrumb for debugging
  void recordBreadcrumb(String name, Map<String, dynamic> attributes) {
    if (!_isInitialized) {
      return;
    }

    NewrelicMobile.instance.recordBreadcrumb(name, eventAttributes: attributes);
  }

  /// Start a custom interaction
  Future<String> startInteraction(String actionName) async {
    if (!_isInitialized) {
      return '';
    }

    return await NewrelicMobile.instance.startInteraction(actionName);
  }

  /// End a custom interaction
  void endInteraction(String interactionId) {
    if (!_isInitialized) {
      return;
    }

    NewrelicMobile.instance.endInteraction(interactionId);
  }

  /// Record an error manually
  void recordError(
    dynamic error,
    StackTrace? stackTrace, {
    Map<String, dynamic>? attributes,
  }) {
    if (!_isInitialized) {
      return;
    }

    NewrelicMobile.instance.recordError(
      error,
      stackTrace,
      attributes: attributes,
    );
  }

  /// Set user information for session tracking
  void setUserId(String userId) {
    if (!_isInitialized) {
      return;
    }

    NewrelicMobile.instance.setUserId(userId);
  }

  /// Set device and network information for comprehensive analytics
  void setDeviceAndNetworkInfo() {
    if (!_isInitialized) {
      return;
    }

    try {
      // Set platform information
      setCustomAttributes({
        'platform': Platform.isIOS ? 'iOS' : 'Android',
        'platform_version': Platform.operatingSystemVersion,
        'app_environment': EnvironmentConfig.currentEnvironment,
      });

      // Record device info event for analytics
      recordCustomEvent('device_info', {
        'platform': Platform.isIOS ? 'iOS' : 'Android',
        'platform_version': Platform.operatingSystemVersion,
        'environment': EnvironmentConfig.currentEnvironment,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      AppLogger.warning('Failed to set device info: $e');
    }
  }

  /// Record a screen view manually (in addition to automatic navigation tracking)
  void recordScreenView(String screenName, {Map<String, dynamic>? attributes}) {
    if (!_isInitialized) {
      return;
    }

    final screenAttributes = {
      'screen_name': screenName,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      ...?attributes,
    };

    NewrelicMobile.instance.recordCustomEvent(
      'screen_view',
      eventAttributes: screenAttributes,
    );
  }

  /// Manually track HTTP requests with comprehensive statistics
  void recordHttpRequest({
    required String url,
    required String method,
    required int statusCode,
    required int startTime,
    required int endTime,
    int? bytesSent,
    int? bytesReceived,
    String? responseBody,
    String? errorMessage,
    Map<String, String>? headers,
  }) {
    if (!_isInitialized) {
      return;
    }

    try {
      final duration = endTime - startTime;

      // Use New Relic's built-in HTTP transaction tracking
      NewrelicMobile.instance.noticeHttpTransaction(
        url,
        method,
        statusCode,
        startTime,
        endTime,
        bytesSent ?? 0,
        bytesReceived ?? 0,
        null, // traceData
        responseBody: responseBody ?? '',
      );

      // Record detailed custom event for additional analytics
      final requestAttributes = <String, dynamic>{
        'url': url,
        'method': method,
        'status_code': statusCode,
        'duration_ms': duration,
        'bytes_sent': bytesSent ?? 0,
        'bytes_received': bytesReceived ?? 0,
        'timestamp': startTime,
        'success': statusCode >= 200 && statusCode < 300,
        'error': statusCode >= 400,
        'server_error': statusCode >= 500,
        'client_error': statusCode >= 400 && statusCode < 500,
      };

      // Add error information if present
      if (errorMessage != null) {
        requestAttributes['error_message'] = errorMessage;
      }

      // Add response time categories for analytics
      if (duration < 100) {
        requestAttributes['response_time_category'] = 'fast';
      } else if (duration < 500) {
        requestAttributes['response_time_category'] = 'medium';
      } else if (duration < 2000) {
        requestAttributes['response_time_category'] = 'slow';
      } else {
        requestAttributes['response_time_category'] = 'very_slow';
      }

      // Extract domain for analytics
      try {
        final uri = Uri.parse(url);
        requestAttributes['domain'] = uri.host;
        requestAttributes['path'] = uri.path;
        requestAttributes['scheme'] = uri.scheme;
      } catch (e) {
        // Ignore parsing errors
      }

      // Record the custom event
      recordCustomEvent('http_request', requestAttributes);
    } catch (e) {
      AppLogger.warning('Failed to record HTTP request to New Relic: $e');
    }
  }

  /// Record network failures with comprehensive error tracking
  void recordNetworkFailure({
    required String url,
    required String method,
    required int startTime,
    required int endTime,
    required String errorMessage,
    String? errorType,
    int? statusCode,
  }) {
    if (!_isInitialized) {
      return;
    }

    try {
      final duration = endTime - startTime;

      // Use New Relic's built-in network failure tracking
      try {
        // Determine failure type
        final failureType = _getNetworkFailureType(errorMessage, statusCode);
        NewrelicMobile.instance.noticeNetworkFailure(
          url,
          method,
          startTime,
          endTime,
          failureType,
        );
      } catch (e) {
        AppLogger.warning('Built-in network failure tracking failed: $e');
      }

      // Record detailed custom event for analytics
      final failureAttributes = <String, dynamic>{
        'url': url,
        'method': method,
        'start_time': startTime,
        'end_time': endTime,
        'duration_ms': duration,
        'error_message': errorMessage,
        'error_type': errorType ?? _categorizeError(errorMessage),
        'timestamp': startTime,
      };

      // Add status code if available
      if (statusCode != null) {
        failureAttributes['status_code'] = statusCode;
        failureAttributes['http_error'] = true;
      } else {
        failureAttributes['network_error'] = true;
      }

      // Extract domain for analytics
      try {
        final uri = Uri.parse(url);
        failureAttributes['domain'] = uri.host;
        failureAttributes['path'] = uri.path;
        failureAttributes['scheme'] = uri.scheme;
      } catch (e) {
        // Ignore parsing errors
      }

      recordCustomEvent('network_failure', failureAttributes);
    } catch (e) {
      AppLogger.warning('Failed to record network failure to New Relic: $e');
    }
  }

  /// Get configuration info for debugging
  Map<String, dynamic> get debugInfo => {
    'isInitialized': _isInitialized,
    'environment': EnvironmentConfig.currentEnvironment,
    'platform': Platform.isIOS ? 'iOS' : 'Android',
    'hasToken': _getAppToken().isNotEmpty,
  };

  /// Helper method to determine NetworkFailure type from error message and status code
  NetworkFailure _getNetworkFailureType(String errorMessage, int? statusCode) {
    final lowerError = errorMessage.toLowerCase();

    if (statusCode != null) {
      if (statusCode >= 400 && statusCode < 500) {
        return NetworkFailure.badServerResponse;
      } else if (statusCode >= 500) {
        return NetworkFailure.badServerResponse;
      }
    }

    if (lowerError.contains('timeout') || lowerError.contains('timed out')) {
      return NetworkFailure.timedOut;
    } else if (lowerError.contains('dns') || lowerError.contains('host')) {
      return NetworkFailure.dnsLookupFailed;
    } else if (lowerError.contains('connection') ||
        lowerError.contains('connect')) {
      return NetworkFailure.cannotConnectToHost;
    } else if (lowerError.contains('ssl') ||
        lowerError.contains('certificate')) {
      return NetworkFailure.secureConnectionFailed;
    } else if (lowerError.contains('url') || lowerError.contains('malformed')) {
      return NetworkFailure.badURL;
    }

    return NetworkFailure.unknown;
  }

  /// Helper method to categorize error types for analytics
  String _categorizeError(String errorMessage) {
    final lowerError = errorMessage.toLowerCase();

    if (lowerError.contains('timeout')) {
      return 'timeout';
    } else if (lowerError.contains('connection')) {
      return 'connection';
    } else if (lowerError.contains('dns')) {
      return 'dns';
    } else if (lowerError.contains('ssl') ||
        lowerError.contains('certificate')) {
      return 'ssl';
    } else if (lowerError.contains('auth')) {
      return 'authentication';
    } else if (lowerError.contains('permission')) {
      return 'permission';
    } else if (lowerError.contains('network')) {
      return 'network';
    }

    return 'unknown';
  }
}
