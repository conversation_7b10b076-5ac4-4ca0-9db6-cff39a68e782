import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:newrelic_mobile/newrelic_mobile.dart';
import 'package:gameflex_mobile/config/environment_config.dart';
import 'package:gameflex_mobile/utils/app_logger.dart';

/// Service for managing New Relic initialization and configuration
/// Handles environment-specific tokens and configuration settings
class NewRelicService {
  static final NewRelicService _instance = NewRelicService._internal();
  static NewRelicService get instance => _instance;
  
  NewRelicService._internal();

  bool _isInitialized = false;
  
  /// Check if New Relic has been initialized
  bool get isInitialized => _isInitialized;

  /// Initialize New Relic with environment-specific configuration
  Future<void> initialize() async {
    if (_isInitialized) {
      AppLogger.info('New Relic already initialized');
      return;
    }

    try {
      final appToken = _getAppToken();
      
      if (appToken.isEmpty) {
        AppLogger.warning('New Relic token not found for current environment and platform');
        return;
      }

      final config = _createConfig(appToken);
      
      AppLogger.info('Initializing New Relic for ${EnvironmentConfig.currentEnvironment} environment');
      AppLogger.info('Platform: ${Platform.isIOS ? 'iOS' : 'Android'}');
      
      await NewrelicMobile.instance.start(config, () {
        AppLogger.info('New Relic initialized successfully');
        _isInitialized = true;
      });
      
    } catch (e) {
      AppLogger.error('Failed to initialize New Relic: $e');
    }
  }

  /// Get the appropriate app token based on current environment and platform
  String _getAppToken() {
    final environment = EnvironmentConfig.currentEnvironment;
    final tokens = EnvironmentConfig.newRelicTokens[environment];
    
    if (tokens == null) {
      AppLogger.warning('No New Relic tokens configured for environment: $environment');
      return '';
    }
    
    if (Platform.isIOS) {
      return tokens['ios'] ?? '';
    } else if (Platform.isAndroid) {
      return tokens['android'] ?? '';
    }
    
    return '';
  }

  /// Create New Relic configuration based on environment
  Config _createConfig(String appToken) {
    final isDevelopment = EnvironmentConfig.isDevelopment;
    
    return Config(
      accessToken: appToken,
      
      // Analytics and Event Collection
      analyticsEventEnabled: true,
      printStatementAsEventsEnabled: isDevelopment, // Only in dev for debugging
      
      // Network Monitoring
      networkErrorRequestEnabled: true,
      networkRequestEnabled: true,
      httpInstrumentationEnabled: true,
      httpResponseBodyCaptureEnabled: isDevelopment, // Only in dev for debugging
      
      // Crash Reporting
      crashReportingEnabled: true,
      
      // Interaction Tracing
      interactionTracingEnabled: true,
      
      // iOS specific options
      webViewInstrumentation: Platform.isIOS,
      
      // Logging - more verbose in development
      loggingEnabled: isDevelopment,
    );
  }

  /// Set custom attributes for the current session
  void setCustomAttributes(Map<String, dynamic> attributes) {
    if (!_isInitialized) {
      AppLogger.warning('New Relic not initialized, cannot set custom attributes');
      return;
    }

    attributes.forEach((key, value) {
      NewrelicMobile.instance.setAttribute(key, value);
    });
  }

  /// Record a custom event
  void recordCustomEvent(String eventType, Map<String, dynamic> attributes) {
    if (!_isInitialized) {
      AppLogger.warning('New Relic not initialized, cannot record custom event');
      return;
    }

    NewrelicMobile.instance.recordCustomEvent(eventType, attributes: attributes);
  }

  /// Record a breadcrumb for debugging
  void recordBreadcrumb(String name, Map<String, dynamic> attributes) {
    if (!_isInitialized) {
      return;
    }

    NewrelicMobile.instance.recordBreadcrumb(name, attributes: attributes);
  }

  /// Start a custom interaction
  String startInteraction(String actionName) {
    if (!_isInitialized) {
      return '';
    }

    return NewrelicMobile.instance.startInteraction(actionName);
  }

  /// End a custom interaction
  void endInteraction(String interactionId) {
    if (!_isInitialized) {
      return;
    }

    NewrelicMobile.instance.endInteraction(interactionId);
  }

  /// Record an error manually
  void recordError(dynamic error, StackTrace? stackTrace, {Map<String, dynamic>? attributes}) {
    if (!_isInitialized) {
      return;
    }

    NewrelicMobile.instance.recordError(
      error,
      stackTrace,
      attributes: attributes,
    );
  }

  /// Set user information for session tracking
  void setUserId(String userId) {
    if (!_isInitialized) {
      return;
    }

    NewrelicMobile.instance.setUserId(userId);
  }

  /// Get configuration info for debugging
  Map<String, dynamic> get debugInfo => {
    'isInitialized': _isInitialized,
    'environment': EnvironmentConfig.currentEnvironment,
    'platform': Platform.isIOS ? 'iOS' : 'Android',
    'hasToken': _getAppToken().isNotEmpty,
  };
}
