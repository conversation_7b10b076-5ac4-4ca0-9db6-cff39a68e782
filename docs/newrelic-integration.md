# New Relic Integration Guide

This document explains how to set up and use New Relic monitoring in the GameFlex mobile app.

## Overview

The New Relic integration provides:
- Environment-specific configuration (dev, staging, production)
- Platform-specific tokens (iOS and Android)
- Automatic crash reporting and performance monitoring
- Custom event tracking and user analytics
- Network request monitoring
- **Automatic screen tracking** via NavigationObserver
- Manual screen view recording capabilities

## Setup

### 1. Get New Relic Application Tokens

1. Log in to your New Relic account
2. Create separate applications for each environment and platform:
   - `GameFlex Dev iOS`
   - `GameFlex Dev Android`
   - `GameFlex Staging iOS`
   - `GameFlex Staging Android`
   - `GameFlex Production iOS`
   - `GameFlex Production Android`

3. Copy the application tokens from each app's settings

### 2. Configure Environment Variables

Update your `.env` files with the actual tokens:

#### Development (.env)
```env
NEWRELIC_DEV_IOS_TOKEN=your_actual_dev_ios_token
NEWRELIC_DEV_ANDROID_TOKEN=your_actual_dev_android_token
```

#### Staging (.env.staging)
```env
NEWRELIC_STAGING_IOS_TOKEN=your_actual_staging_ios_token
NEWRELIC_STAGING_ANDROID_TOKEN=your_actual_staging_android_token
```

#### Production (.env.production)
```env
NEWRELIC_PROD_IOS_TOKEN=your_actual_prod_ios_token
NEWRELIC_PROD_ANDROID_TOKEN=your_actual_prod_android_token
```

### 3. Android Configuration

The Android build configuration has been automatically set up:

- **Gradle Plugin**: New Relic Android agent plugin added to build files
- **Permissions**: Required network permissions already configured
- **Build Flavors**: Works with your existing development/staging/production flavors

See `docs/android-newrelic-setup.md` for detailed Android configuration.

### 4. Build Configuration

The app automatically detects the environment and uses the appropriate tokens:

- **Development**: Uses dev tokens, enables verbose logging and debugging features
- **Staging**: Uses staging tokens, disables debug features
- **Production**: Uses production tokens, minimal logging for performance

## Usage

### Automatic Features

The following are automatically tracked:
- App crashes and errors
- Network requests and responses
- User interactions and navigation
- App performance metrics
- **Screen navigation** - All route changes are automatically tracked via `NewRelicNavigationObserver`

### Manual Tracking

You can add custom tracking in your code:

```dart
import 'package:gameflex_mobile/services/newrelic_service.dart';

// Record custom events
NewRelicService.instance.recordCustomEvent('user_action', {
  'action': 'post_created',
  'channel': 'gaming',
  'user_id': userId,
});

// Set user information
NewRelicService.instance.setUserId(userId);

// Record breadcrumbs for debugging
NewRelicService.instance.recordBreadcrumb('navigation', {
  'from': 'home',
  'to': 'profile',
});

// Track custom interactions
final interactionId = await NewRelicService.instance.startInteraction('image_upload');
// ... perform upload ...
NewRelicService.instance.endInteraction(interactionId);

// Record manual screen views (in addition to automatic tracking)
NewRelicService.instance.recordScreenView('ProfileScreen', attributes: {
  'user_id': userId,
  'tab': 'settings',
});

// Record errors manually
try {
  // risky operation
} catch (e, stackTrace) {
  NewRelicService.instance.recordError(e, stackTrace, attributes: {
    'context': 'image_processing',
    'user_id': userId,
  });
}
```

### Custom Attributes

Set session-level attributes:

```dart
NewRelicService.instance.setCustomAttributes({
  'user_tier': 'premium',
  'app_version': '1.0.0',
  'device_type': 'mobile',
});
```

## Environment Detection

The app automatically detects the environment using:
- `dart.vm.product` for development detection
- `STAGING` environment variable for staging
- `PRODUCTION` environment variable for production

## Screen Tracking

### Automatic Screen Tracking
Screen navigation is automatically tracked using `NewRelicNavigationObserver`. This captures:
- Route changes and navigation events
- Screen names and transitions
- Time spent on each screen
- Navigation patterns

The observer is automatically added to your `MaterialApp` in `main.dart`:
```dart
MaterialApp(
  navigatorObservers: [
    routeObserver,
    NewRelicNavigationObserver(), // <-- Automatic screen tracking
  ],
  // ...
)
```

### Manual Screen Tracking
For additional screen tracking or custom screen events:
```dart
// Record custom screen views with additional context
NewRelicService.instance.recordScreenView('UserProfileScreen', attributes: {
  'user_id': currentUserId,
  'profile_type': 'public',
  'source': 'search_results',
});
```

## Configuration Details

### Development Environment
- Enables verbose logging
- Enables print statement tracking
- Full interaction tracing
- Manual network request tracking (automatic HTTP instrumentation disabled)

### Staging/Production Environments
- Minimal logging for performance
- Essential monitoring only
- Manual network request tracking
- Optimized for production use

### Network Request Tracking

**Note**: Automatic HTTP instrumentation is disabled to prevent conflicts with custom error handling. Use manual tracking instead:

```dart
// Track successful HTTP requests
NewRelicService.instance.recordHttpRequest(
  url: 'https://api.example.com/posts',
  method: 'GET',
  statusCode: 200,
  startTime: startTime,
  endTime: endTime,
  bytesSent: requestSize,
  bytesReceived: responseSize,
);

// Track network failures
NewRelicService.instance.recordNetworkFailure(
  url: 'https://api.example.com/posts',
  method: 'GET',
  startTime: startTime,
  endTime: endTime,
  errorMessage: 'Connection timeout',
);
```

## Optional: Manual HTTP Tracking Integration

If you want to add manual HTTP tracking to your API service, you can modify your API calls like this:

```dart
// In your API service
import '../services/newrelic_service.dart';

Future<http.Response> makeRequest(...) async {
  final startTime = DateTime.now().millisecondsSinceEpoch;

  try {
    final response = await http.get(uri, headers: headers);

    // Track successful request
    NewRelicService.instance.recordHttpRequest(
      url: uri.toString(),
      method: 'GET',
      statusCode: response.statusCode,
      startTime: startTime,
      endTime: DateTime.now().millisecondsSinceEpoch,
      bytesReceived: response.bodyBytes.length,
    );

    return response;
  } catch (e) {
    // Track failed request
    NewRelicService.instance.recordNetworkFailure(
      url: uri.toString(),
      method: 'GET',
      startTime: startTime,
      endTime: DateTime.now().millisecondsSinceEpoch,
      errorMessage: e.toString(),
    );
    rethrow;
  }
}
```

## Troubleshooting

### Check Initialization Status
```dart
final debugInfo = NewRelicService.instance.debugInfo;
print('New Relic Status: $debugInfo');
```

### Common Issues

1. **No data in New Relic dashboard**
   - Verify tokens are correct in environment files
   - Check that the app is using the right environment
   - Ensure network connectivity

2. **Missing iOS data**
   - Verify iOS token is set correctly
   - Check iOS app configuration in New Relic

3. **Missing Android data**
   - Verify Android token is set correctly
   - Check Android app configuration in New Relic

### Debug Information

The service provides debug information:
```dart
final info = NewRelicService.instance.debugInfo;
// Returns:
// {
//   'isInitialized': true,
//   'environment': 'development',
//   'platform': 'iOS',
//   'hasToken': true
// }
```

## Best Practices

1. **Use meaningful event names** for custom events
2. **Include relevant context** in custom attributes
3. **Don't log sensitive information** (passwords, tokens, etc.)
4. **Use breadcrumbs** to track user flow for debugging
5. **Set user IDs** after authentication for better tracking
6. **Monitor performance** of custom interactions

## Security Notes

- Never commit actual tokens to version control
- Use environment-specific tokens
- Rotate tokens periodically
- Monitor New Relic access logs
